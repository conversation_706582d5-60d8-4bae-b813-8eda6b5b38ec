// This script runs on HDrezka pages
console.log('HDrezka Easy Downloader loaded');

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message: { action: string; url: any; }) => {
  if (message.action === 'newMp4Url') {
    console.log('New base MP4 URL detected:', message.url);
    // Refresh the download button with the new URL
    addDownloadButtons();
  }
});

// Function to create a download button
function createDownloadButton(url: string): HTMLElement {
  const button = document.createElement('button');
  button.textContent = 'Download Video (MP4)';
  button.className = 'hdrezka-downloader-btn';
  button.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; padding: 10px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);';
  button.title = `Download: ${url}`;

  button.addEventListener('click', () => {
    console.log('Downloading video from:', url);
    // Create a temporary anchor to trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = 'hdrezka-video.mp4';
    a.target = '_blank';
    a.click();
  });

  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.background = '#45a049';
  });

  button.addEventListener('mouseleave', () => {
    button.style.background = '#4CAF50';
  });

  return button;
}

// Check for video elements and add download buttons
function addDownloadButtons() {
  chrome.runtime.sendMessage({ action: 'getMp4Urls' }, (response: { urls: string | any[]; }) => {
    if (response && response.urls && response.urls.length > 0) {
      // Get the latest URL (most recent base MP4 URL)
      const latestUrl = response.urls[response.urls.length - 1];
      console.log('Adding download button for:', latestUrl);

      // Remove any existing buttons
      const existingButtons = document.querySelectorAll('.hdrezka-downloader-btn');
      existingButtons.forEach(btn => btn.remove());

      // Add new button
      const button = createDownloadButton(latestUrl);
      document.body.appendChild(button);
    } else {
      console.log('No MP4 URLs found yet. Waiting for video to load...');
    }
  });
}

// Run when page loads
window.addEventListener('load', () => {
  setTimeout(addDownloadButtons, 2000); // Wait for page to fully load
});
