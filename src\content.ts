// This script runs on HDrezka pages
console.log('HDrezka Easy Downloader loaded');

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message: { action: string; url: any; }) => {
  if (message.action === 'newMp4Url') {
    console.log('New base MP4 URL detected:', message.url);
    // Refresh the download button with the new URL
    addDownloadButtons();
  }
});

// Function to extract movie title from the page
function getMovieTitle(): string {
  // Try the original page format first
  let titleElement = document.querySelector('.b-post__title h1[itemprop="name"]');
  if (titleElement?.textContent?.trim()) {
    return titleElement.textContent.trim();
  }

  // Try the sound/voice selection page format
  titleElement = document.querySelector('.b-post__title h1');
  if (titleElement?.textContent?.trim()) {
    // Extract just the movie title, removing voice/sound info
    const fullTitle = titleElement.textContent.trim();
    // Remove common voice/sound suffixes like "в озвучке Дубляж", "Субтитры", etc.
    const cleanTitle = fullTitle
      .replace(/\s+в\s+озвучке\s+.+$/i, '') // Remove "в озвучке ..."
      .replace(/\s+субтитры$/i, '') // Remove "Субтитры"
      .replace(/\s+дубляж$/i, '') // Remove "Дубляж"
      .trim();
    return cleanTitle || fullTitle;
  }

  return 'Unknown Movie';
}

// Function to extract movie year from the page
function getMovieYear(): string {
  // Try the original page format first - look for year link in the format "2012 года"
  const yearLinks = document.querySelectorAll('a[href*="/year/"]');
  for (const link of yearLinks) {
    const text = link.textContent?.trim();
    if (text && text.includes('года')) {
      const year = text.replace('года', '').trim();
      if (/^\d{4}$/.test(year)) {
        return year;
      }
    }
  }

  // Try the sound/voice selection page format - look in .b-post__origtitle
  const origTitleElement = document.querySelector('.b-post__origtitle');
  if (origTitleElement?.textContent?.trim()) {
    const origTitle = origTitleElement.textContent.trim();
    // Extract year from format like "The Bikeriders (2023)"
    const yearMatch = origTitle.match(/\((\d{4})\)/);
    if (yearMatch && yearMatch[1]) {
      return yearMatch[1];
    }
  }

  return 'Unknown Year';
}

// Function to create a safe filename
function createFilename(title: string, year: string): string {
  // Clean the title to make it safe for filenames
  const cleanTitle = title
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
    .replace(/\s+/g, ' ') // Normalize spaces
    .trim();

  return `${cleanTitle} (${year}).mp4`;
}

// Function to create a download button
function createDownloadButton(url: string): HTMLElement {
  const button = document.createElement('button');
  button.textContent = 'Download Video (MP4)';
  button.className = 'hdrezka-downloader-btn';
  button.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; padding: 10px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);';

  const title = getMovieTitle();
  const year = getMovieYear();
  const filename = createFilename(title, year);

  button.title = `Download: ${filename}`;

  button.addEventListener('click', () => {
    console.log('Downloading video:', filename);
    console.log('URL:', url);

    // Send download request to background script
    chrome.runtime.sendMessage({
      action: 'downloadVideo',
      url: url,
      filename: filename
    });
  });

  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.background = '#45a049';
  });

  button.addEventListener('mouseleave', () => {
    button.style.background = '#4CAF50';
  });

  return button;
}

// Check for video elements and add download buttons
function addDownloadButtons() {
  chrome.runtime.sendMessage({ action: 'getMp4Urls' }, (response: { urls: string | any[]; }) => {
    if (response && response.urls && response.urls.length > 0) {
      // Get the latest URL (most recent base MP4 URL)
      const latestUrl = response.urls[response.urls.length - 1];
      console.log('Adding download button for:', latestUrl);

      // Remove any existing buttons
      const existingButtons = document.querySelectorAll('.hdrezka-downloader-btn');
      existingButtons.forEach(btn => btn.remove());

      // Add new button
      const button = createDownloadButton(latestUrl);
      document.body.appendChild(button);
    } else {
      console.log('No MP4 URLs found yet. Waiting for video to load...');
    }
  });
}

// Run when page loads
window.addEventListener('load', () => {
  setTimeout(addDownloadButtons, 2000); // Wait for page to fully load
});
