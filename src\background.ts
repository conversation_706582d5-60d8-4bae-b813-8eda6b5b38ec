// Store captured MP4 URLs
let mp4Urls: string[] = [];

// Function to extract base MP4 URL from HLS segment URL
function extractBaseMp4Url(url: string): string | null {
  // Check if this is an HLS segment URL (contains .mp4 and ends with .ts)
  if (url.includes('.mp4') && url.includes(':hls:') && url.endsWith('.ts')) {
    // Extract the part before :hls:
    const hlsIndex = url.indexOf(':hls:');
    if (hlsIndex !== -1) {
      return url.substring(0, hlsIndex);
    }
  }

  // If it's a direct .mp4 URL (not HLS), return as is
  if (url.includes('.mp4') && !url.includes(':hls:')) {
    return url;
  }

  return null;
}

// Listen for web requests to capture MP4 URLs
chrome.webRequest.onBeforeRequest.addListener(
  (details: { url: string; }) => {
    const baseMp4Url = extractBaseMp4Url(details.url);

    if (baseMp4Url) {
      // Add URL to our list if it's not already there
      if (!mp4Urls.includes(baseMp4Url)) {
        mp4Urls.push(baseMp4Url);
        console.log('Captured base MP4 URL:', baseMp4Url);

        // Store in chrome.storage for persistence
        chrome.storage.local.set({ mp4Urls });

        // Notify content script about new URL
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]?.id) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'newMp4Url',
              url: baseMp4Url
            });
          }
        });
      }
    }
    return { cancel: false };
  },
  { urls: ["*://*.hdrezka.ag/*", "*://*.rezka.ag/*", "*://*.hdrezka.website/*", "*://*.rezka.website/*", "<all_urls>"] }
);

// Listen for messages from popup or content scripts
chrome.runtime.onMessage.addListener((message: { action: string; url?: string; filename?: string; }, _sender: any, sendResponse: (arg0: { urls: any; }) => void) => {
  if (message.action === 'getMp4Urls') {
    // Return the list of MP4 URLs
    chrome.storage.local.get('mp4Urls', (items: { [key: string]: any }) => {
      sendResponse({ urls: items.mp4Urls || [] });
    });
    return true; // Required for async sendResponse
  }

  if (message.action === 'downloadVideo' && message.url && message.filename) {
    // Use Chrome's downloads API to download the file
    chrome.downloads.download({
      url: message.url,
      filename: message.filename,
      saveAs: false // Don't show save dialog, use default download location
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        console.error('Download failed:', chrome.runtime.lastError);
      } else {
        console.log('Download started with ID:', downloadId);
      }
    });
  }
});

