# HDrezka Easy Downloader

A Chrome extension that allows you to easily download videos from HDrezka streaming sites by extracting the base MP4 URLs from HLS streams.

## Features

- **Smart URL Extraction**: Automatically detects HLS streaming segments and extracts the base MP4 URL
- **One-Click Download**: Adds a convenient download button to HDrezka pages
- **Duplicate Prevention**: Only stores unique base MP4 URLs to avoid multiple downloads of the same video
- **Real-time Detection**: Monitors network requests and updates the download button when new videos are detected

## How It Works

HDrezka uses HLS (HTTP Live Streaming) which splits videos into multiple segments. The extension:

1. **Monitors Network Requests**: Listens for requests to `.mp4` files and HLS segments (`.ts` files)
2. **Extracts Base URLs**: Converts HLS segment URLs like:
   ```
   https://example.com/video.mp4:hls:seg-5-v1-a1.ts
   ```
   Into base MP4 URLs like:
   ```
   https://example.com/video.mp4
   ```
3. **Extracts Movie Metadata**: Automatically detects movie title and year from the page:
   - **Original pages**: Title from `.b-post__title h1[itemprop="name"]`, year from year links
   - **Sound/voice pages**: Title from `.b-post__title h1` (cleaned), year from `.b-post__origtitle`
4. **Provides Download Button**: Adds a green "Download Video (MP4)" button to the page
5. **Seamless Download**: Clicking the button directly downloads the complete MP4 file with proper filename

## Installation

1. Clone or download this repository
2. Run `npm install` to install dependencies
3. Run `npm run build` to build the extension
4. Open Chrome and go to `chrome://extensions/`
5. Enable "Developer mode"
6. Click "Load unpacked" and select the `dist` folder

## Usage

1. Navigate to any HDrezka site (hdrezka.ag, rezka.ag, etc.)
2. Start playing a video
3. The extension will automatically detect the video stream
4. A green "Download Video (MP4)" button will appear in the top-right corner
5. Click the button to download the complete MP4 file

## Supported Sites

- hdrezka.ag
- rezka.ag
- hdrezka.website
- rezka.website
- And their subdomains

## Technical Details

- **Manifest Version**: 3 (latest Chrome extension standard)
- **Content Security Policy**: Strict CSP to prevent eval() usage
- **Build System**: TypeScript + Webpack with production optimization
- **Permissions**: Minimal required permissions for web request monitoring and storage

## Development

```bash
# Install dependencies
npm install

# Build for development
npm run build

# The built extension will be in the dist/ folder
```

## Files Structure

- `src/background.ts` - Service worker that monitors network requests
- `src/content.ts` - Content script that adds the download button
- `src/popup.ts` - Extension popup interface
- `src/manifest.json` - Extension configuration
- `webpack.config.js` - Build configuration

## License

This project is for educational purposes. Please respect copyright laws and only download content you have the right to download.